import hashlib
import base64

# 测试参数
org_code = "12100000455416061W"
encrypt = "0"
verify_type = "1"
secret_key = "656C708A8E1A11F08D33080058000005"
base_url = "https://172.16.99.201:9871/files/cloudFilm/index.html#/pages/StudyInfo"
study_id = "387"

# 生成签名字符串
signature_string = f"orgCode={org_code}&encrypt={encrypt}&verifyType={verify_type}&studyId={study_id}&secretKey={secret_key}"
print(f"签名字符串: {signature_string}")

# 生成MD5签名
md5_signature = hashlib.md5(signature_string.encode('utf-8')).hexdigest().upper()
print(f"MD5签名: {md5_signature}")

# 生成参数字符串
params_string = f"orgCode={org_code}&encrypt={encrypt}&verifyType={verify_type}&studyId={study_id}&signature={md5_signature}"
print(f"参数字符串: {params_string}")

# Base64编码
encoded_data = base64.b64encode(params_string.encode('utf-8')).decode('utf-8')
print(f"Base64编码: {encoded_data}")

# 最终URL
final_url = f"{base_url}?data={encoded_data}"
print(f"最终URL: {final_url}")









#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用 pypandoc 的 Markdown 到 Word 文档转换器
提供更好的格式转换效果和完整的 Markdown 语法支持

作者: AI Assistant
日期: 2025-10-01
功能: 将云胶片对接简版文档.md 转换为高质量的 Word 格式
"""

import os
import sys
import subprocess
import logging
import time
import urllib.request
from pathlib import Path
from typing import Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pypandoc_conversion.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class PypandocConverter:
    """使用 pypandoc 的高质量 Markdown 到 Word 转换器"""
    
    def __init__(self):
        """初始化转换器"""
        self.input_file = "云胶片对接简版文档.md"
        self.output_file = "云胶片对接简版文档.docx"
        self.current_dir = Path.cwd()
        
    def install_pypandoc(self) -> bool:
        """
        安装 pypandoc 库
        
        Returns:
            bool: 安装是否成功
        """
        logger.info("正在安装 pypandoc...")
        
        try:
            import pypandoc
            logger.info("pypandoc 已安装")
            return True
        except ImportError:
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", "pypandoc"
                ], timeout=120)
                logger.info("pypandoc 安装成功")
                return True
            except Exception as e:
                logger.error(f"pypandoc 安装失败: {e}")
                return False
    
    def download_pandoc_manually(self) -> bool:
        """
        手动下载 pandoc 可执行文件
        
        Returns:
            bool: 下载是否成功
        """
        logger.info("尝试手动下载 pandoc...")
        
        try:
            import pypandoc
            
            # 尝试多个下载源
            download_urls = [
                "https://github.com/jgm/pandoc/releases/download/3.1.8/pandoc-3.1.8-windows-x86_64.zip",
                "https://github.com/jgm/pandoc/releases/download/2.19.2/pandoc-2.19.2-windows-x86_64.zip"
            ]
            
            for url in download_urls:
                try:
                    logger.info(f"尝试从 {url} 下载...")
                    pypandoc.download_pandoc(url=url, targetfolder=None)
                    logger.info("pandoc 下载成功")
                    return True
                except Exception as e:
                    logger.warning(f"从 {url} 下载失败: {e}")
                    continue
            
            # 如果所有 URL 都失败，尝试默认下载
            logger.info("尝试默认下载方式...")
            pypandoc.download_pandoc()
            logger.info("pandoc 默认下载成功")
            return True
            
        except Exception as e:
            logger.error(f"手动下载 pandoc 失败: {e}")
            return False
    
    def setup_pandoc(self) -> bool:
        """
        设置 pandoc 环境
        
        Returns:
            bool: 设置是否成功
        """
        logger.info("正在设置 pandoc 环境...")
        
        try:
            import pypandoc
            
            # 首先尝试获取版本
            try:
                version = pypandoc.get_pandoc_version()
                logger.info(f"pandoc 已安装，版本: {version}")
                return True
            except OSError:
                logger.info("pandoc 未安装，开始下载...")
                
                # 尝试自动下载
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        logger.info(f"下载尝试 {attempt + 1}/{max_retries}")
                        
                        if self.download_pandoc_manually():
                            # 验证下载是否成功
                            version = pypandoc.get_pandoc_version()
                            logger.info(f"pandoc 下载并验证成功，版本: {version}")
                            return True
                        
                    except Exception as e:
                        logger.warning(f"下载尝试 {attempt + 1} 失败: {e}")
                        if attempt < max_retries - 1:
                            logger.info("等待 5 秒后重试...")
                            time.sleep(5)
                
                logger.error("所有下载尝试都失败了")
                return False
                
        except Exception as e:
            logger.error(f"设置 pandoc 环境失败: {e}")
            return False
    
    def convert_with_pypandoc(self) -> bool:
        """
        使用 pypandoc 执行转换
        
        Returns:
            bool: 转换是否成功
        """
        try:
            import pypandoc
            
            input_path = str(self.current_dir / self.input_file)
            output_path = str(self.current_dir / self.output_file)
            
            logger.info(f"开始转换: {input_path} -> {output_path}")
            
            # 设置转换参数，优化中文支持和格式
            extra_args = [
                '--from=markdown+smart+raw_html+markdown_in_html_blocks+fenced_code_blocks+backtick_code_blocks+fenced_code_attributes',
                '--to=docx',
                '--standalone',
                '--highlight-style=tango',
                '--reference-doc=',  # 可以指定参考文档模板
                '--lua-filter=',     # 可以添加 Lua 过滤器
                '--wrap=preserve',   # 保持换行
                '--columns=80',      # 设置列宽
                '--tab-stop=4',      # 设置制表符宽度
            ]
            
            # 执行转换
            output = pypandoc.convert_file(
                input_path,
                'docx',
                outputfile=output_path,
                extra_args=extra_args,
                encoding='utf-8'
            )
            
            logger.info("pypandoc 转换完成")
            return True
            
        except Exception as e:
            logger.error(f"pypandoc 转换失败: {e}")
            return False
    
    def validate_input_file(self) -> bool:
        """
        验证输入文件
        
        Returns:
            bool: 验证是否成功
        """
        input_path = self.current_dir / self.input_file
        
        if not input_path.exists():
            logger.error(f"输入文件不存在: {input_path}")
            return False
            
        if not input_path.is_file():
            logger.error(f"输入路径不是文件: {input_path}")
            return False
            
        file_size = input_path.stat().st_size
        if file_size == 0:
            logger.error("输入文件为空")
            return False
            
        logger.info(f"输入文件验证成功: {input_path} (大小: {file_size} 字节)")
        return True
    
    def validate_output_file(self) -> bool:
        """
        验证输出文件
        
        Returns:
            bool: 验证是否成功
        """
        output_path = self.current_dir / self.output_file
        
        if not output_path.exists():
            logger.error(f"输出文件未生成: {output_path}")
            return False
            
        if not output_path.is_file():
            logger.error(f"输出路径不是文件: {output_path}")
            return False
            
        file_size = output_path.stat().st_size
        if file_size == 0:
            logger.error("输出文件为空")
            return False
            
        if output_path.suffix.lower() != '.docx':
            logger.error(f"输出文件扩展名错误: {output_path.suffix}")
            return False
            
        logger.info(f"输出文件验证成功: {output_path} (大小: {file_size} 字节)")
        return True
    
    def run(self) -> bool:
        """
        执行完整的转换流程
        
        Returns:
            bool: 转换是否成功
        """
        logger.info("=" * 60)
        logger.info("开始使用 pypandoc 进行 Markdown 到 Word 文档转换")
        logger.info("=" * 60)
        
        # 步骤1: 验证输入文件
        logger.info("步骤 1/5: 验证输入文件")
        if not self.validate_input_file():
            logger.error("输入文件验证失败")
            return False
        
        # 步骤2: 安装 pypandoc
        logger.info("步骤 2/5: 安装 pypandoc")
        if not self.install_pypandoc():
            logger.error("pypandoc 安装失败")
            return False
        
        # 步骤3: 设置 pandoc 环境
        logger.info("步骤 3/5: 设置 pandoc 环境")
        if not self.setup_pandoc():
            logger.error("pandoc 环境设置失败")
            return False
        
        # 步骤4: 执行转换
        logger.info("步骤 4/5: 执行文档转换")
        if not self.convert_with_pypandoc():
            logger.error("文档转换失败")
            return False
        
        # 步骤5: 验证输出文件
        logger.info("步骤 5/5: 验证输出文件")
        if not self.validate_output_file():
            logger.error("输出文件验证失败")
            return False
        
        logger.info("=" * 60)
        logger.info("🎉 转换成功完成！")
        logger.info(f"📄 输出文件: {self.current_dir / self.output_file}")
        logger.info("=" * 60)
        
        return True


def main():
    """主函数"""
    try:
        converter = PypandocConverter()
        success = converter.run()
        
        if success:
            print("\n✅ 高质量转换成功完成！")
            print(f"📄 输出文件: {converter.output_file}")
            print("💡 提示: 转换后的 Word 文档保持了完整的 Markdown 格式")
            sys.exit(0)
        else:
            print("\n❌ 转换失败，请查看日志了解详情")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行过程中发生未预期的错误: {e}")
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

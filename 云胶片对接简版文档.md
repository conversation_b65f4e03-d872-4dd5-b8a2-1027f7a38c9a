# 云胶片系统对接文档（简版）

### 功能简介
云胶片系统是中山大学附属口腔医院为患者提供的在线影像查看服务。通过该系统，患者可以通过移动端（APP/小程序）查看自己的检查报告及影像资料，包括X光片、CT扫描等。

### 技术架构
- **访问方式**: 基于Web的嵌入式页面
- **认证机制**: MD5签名验证
- **数据传输**: Base64编码参数
- **访问环境**: 外网环境

### 核心参数
### 固定配置参数

| 参数名 | 值 | 说明 | 是否必填 |
|--------|----|----|---------|
| `orgCode` | `12100000455416061W` | 中山大学附属口腔医院18位统一社会信用代码 | 是 |
| `encrypt` | `0` | 加密方式标识，0表示MD5 | 是 |
| `verifyType` | `0` | 验证方式，0=默认不验证，1=身份证，2=手机号，3=双验证 | 是 |
| `secretKey` | `656C708A8E1A11F08D33080058000005` | MD5密钥（仅用于签名生成，不传输） | 是 |

### 动态参数

| 参数名 | 类型 | 说明 | 示例 | 是否必填 |
|--------|------|------|------|---------|
| `studyId` | String | 检查唯一号，由医院PACS系统生成 | `99207` | 是 |
| `signature` | String | MD5签名，32位大写十六进制字符串 | `0063E72C115F410407D14A9B21584F59` | 是 |

### 实现步骤
1. **构建签名字符串**：`orgCode=值&encrypt=值&verifyType=值&studyId=检查号&secretKey=值`
2. **生成MD5签名**：对签名字符串进行MD5加密，转大写
3. **构建参数字符串**：`orgCode=值&encrypt=值&verifyType=值&studyId=检查号&signature=MD5签名`
4. **Base64编码**：对参数字符串进行Base64编码
5. **拼接URL**：`基础URL + ?data= + Base64编码结果`

### ?? 重要提醒
- **MD5安全性不足**，建议后续升级到SM2或RSA
- **密钥严格保密**，不要在日志中记录
- **参数顺序固定**，不能随意调整
- **MD5结果必须大写**

---

## 代码示例

### Python版本
```python
import hashlib
import base64

def generate_cloud_film_url(study_id):
    # 固定配置
    org_code = "12100000455416061W"
    encrypt = "0"
    verify_type = "0"
    secret_key = "656C708A8E1A11F08D33080058000005"
    base_url = "http://172.16.99.201:9870/files/cloudFilm/index.html#/pages/StudyInfo"
    
    # 1. 构建签名字符串
    sign_str = f"orgCode={org_code}&encrypt={encrypt}&verifyType={verify_type}&studyId={study_id}&secretKey={secret_key}"
    
    # 2. 生成MD5签名（大写）
    signature = hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
    
    # 3. 构建参数字符串
    params_str = f"orgCode={org_code}&encrypt={encrypt}&verifyType={verify_type}&studyId={study_id}&signature={signature}"
    
    # 4. Base64编码
    encoded_data = base64.b64encode(params_str.encode('utf-8')).decode('utf-8')
    
    # 5. 拼接完整URL
    return f"{base_url}?data={encoded_data}"

# 使用示例
url = generate_cloud_film_url("99207")
print(url)
```

---

## 快速验证

### 测试用例

| 步骤 | 参数名称 | 值 |
|------|----------|-----|
| 1 | study_id | `99207` |
| 2 | signature_string | `orgCode=12100000455416061W&encrypt=0&verifyType=0&studyId=99207&secretKey=656C708A8E1A11F08D33080058000005` |
| 3 | md5_signature | `0063E72C115F410407D14A9B21584F59` |
| 4 | params_string | `orgCode=12100000455416061W&encrypt=0&verifyType=0&studyId=99207&signature=0063E72C115F410407D14A9B21584F59` |
| 5 | base64_encoded | `b3JnQ29kZT0xMjEwMDAwMDQ1NTQxNjA2MVcmZW5jcnlwdD0wJnZlcmlmeVR5cGU9MCZzdHVkeUlkPTk5MjA3JnNpZ25hdHVyZT0wMDYzRTcyQzExNUY0MTA0MDdEMTRBOUIyMTU4NEY1OQ==` |
| 6 | final_url | `http://172.16.99.201:9870/files/cloudFilm/index.html#/pages/StudyInfo?data=b3JnQ29kZT0xMjEwMDAwMDQ1NTQxNjA2MVcmZW5jcnlwdD0wJnZlcmlmeVR5cGU9MCZzdHVkeUlkPTk5MjA3JnNpZ25hdHVyZT0wMDYzRTcyQzExNUY0MTA0MDdEMTRBOUIyMTU4NEY1OQ==` |



### 验证步骤
1. 使用测试用例验证MD5签名是否正确
2. 检查Base64编码结果是否匹配
3. 确认生成的URL可以正常访问

---

## 常见问题

**Q: 签名验证失败？**
A: 检查参数顺序和MD5是否大写

**Q: 链接无法访问？**
A: 确认在外网环境连接

**Q: 编码错误？**
A: 确保使用UTF-8编码

---

## 技术支持
如有问题请联系医院PACS项目技术支持：李照明（18292668614）。

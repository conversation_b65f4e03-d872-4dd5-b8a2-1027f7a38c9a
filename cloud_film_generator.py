﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云胶片链接生成器
用于根据检查唯一号生成中山大学附属口腔医院云胶片查看链接
"""

import hashlib
import base64
from urllib.parse import quote


class CloudFilmGenerator:
    """云胶片链接生成器类"""
    
    def __init__(self):
        """初始化配置参数"""
        # 固定配置参数
        self.org_code = "12100000455416061W"
        self.encrypt = "0"
        self.verify_type = "0"
        self.secret_key = "656C708A8E1A11F08D33080058000005"
        self.base_url = "http://172.16.99.201:9870/files/cloudFilm/index.html#/pages/StudyInfo"
    
    def generate_signature(self, study_id):
        """
        生成MD5签名
        
        Args:
            study_id (str): 检查唯一号
            
        Returns:
            str: MD5签名字符串（大写）
        """
        # 按照固定顺序拼接签名字符串
        signature_string = (
            f"orgCode={self.org_code}&"
            f"encrypt={self.encrypt}&"
            f"verifyType={self.verify_type}&"
            f"studyId={study_id}&"
            f"secretKey={self.secret_key}"
        )
        
        # 生成MD5签名
        md5_hash = hashlib.md5(signature_string.encode('utf-8')).hexdigest()
        return md5_hash.upper()  # 返回大写MD5
    
    def encode_parameters(self, study_id, signature):
        """
        编码参数字符串为base64
        
        Args:
            study_id (str): 检查唯一号
            signature (str): MD5签名
            
        Returns:
            str: base64编码后的参数字符串
        """
        # 拼接完整参数字符串
        params_string = (
            f"orgCode={self.org_code}&"
            f"encrypt={self.encrypt}&"
            f"verifyType={self.verify_type}&"
            f"studyId={study_id}&"
            f"signature={signature}"
        )
        
        # Base64编码
        encoded_params = base64.b64encode(params_string.encode('utf-8')).decode('utf-8')
        return encoded_params
    
    def generate_cloud_film_url(self, study_id):
        """
        生成完整的云胶片访问链接
        
        Args:
            study_id (str): 检查唯一号
            
        Returns:
            str: 完整的云胶片访问URL
        """
        if not study_id or not study_id.strip():
            raise ValueError("检查唯一号不能为空")
        
        # 去除首尾空格
        study_id = study_id.strip()
        
        # 生成MD5签名
        signature = self.generate_signature(study_id)
        
        # 编码参数
        encoded_data = self.encode_parameters(study_id, signature)
        
        # 拼接完整URL
        full_url = f"{self.base_url}?data={encoded_data}"
        
        return full_url
    
    def generate_with_details(self, study_id):
        """
        生成链接并返回详细信息（用于调试）
        
        Args:
            study_id (str): 检查唯一号
            
        Returns:
            dict: 包含详细生成过程的字典
        """
        if not study_id or not study_id.strip():
            raise ValueError("检查唯一号不能为空")
        
        study_id = study_id.strip()
        
        # 生成签名字符串
        signature_string = (
            f"orgCode={self.org_code}&"
            f"encrypt={self.encrypt}&"
            f"verifyType={self.verify_type}&"
            f"studyId={study_id}&"
            f"secretKey={self.secret_key}"
        )
        
        # 生成MD5签名
        signature = self.generate_signature(study_id)
        
        # 参数字符串
        params_string = (
            f"orgCode={self.org_code}&"
            f"encrypt={self.encrypt}&"
            f"verifyType={self.verify_type}&"
            f"studyId={study_id}&"
            f"signature={signature}"
        )
        
        # Base64编码
        encoded_data = self.encode_parameters(study_id, signature)
        
        # 完整URL
        full_url = f"{self.base_url}?data={encoded_data}"
        
        return {
            "study_id": study_id,
            "signature_string": signature_string,
            "md5_signature": signature,
            "params_string": params_string,
            "base64_encoded": encoded_data,
            "final_url": full_url
        }


def main():
    """主函数 - 示例用法"""
    generator = CloudFilmGenerator()
    
    print("=== 云胶片链接生成器 ===")
    print()
    
    # 示例1：基本用法
    try:
        study_id = "99207"
        url = generator.generate_cloud_film_url(study_id)
        print(f"检查号: {study_id}")
        print(f"生成的链接: {url}")
        print()
    except Exception as e:
        print(f"生成链接时出错: {e}")
    
    # 示例2：详细信息
    try:
        study_id = "99207"
        details = generator.generate_with_details(study_id)
        print("=== 详细生成过程 ===")
        for key, value in details.items():
            print(f"{key}: {value}")
        print()
    except Exception as e:
        print(f"生成详细信息时出错: {e}")
    
    # 示例3：交互式输入
    print("=== 交互式生成 ===")
    while True:
        try:
            user_study_id = input("请输入检查唯一号 (输入 'quit' 退出): ").strip()
            if user_study_id.lower() == 'quit':
                break
            
            if not user_study_id:
                print("检查唯一号不能为空，请重新输入")
                continue
            
            url = generator.generate_cloud_film_url(user_study_id)
            print(f"生成的云胶片链接:")
            print(url)
            print("-" * 50)
            
        except KeyboardInterrupt:
            print("\n程序已退出")
            break
        except Exception as e:
            print(f"错误: {e}")
            print("-" * 50)


if __name__ == "__main__":
    main()

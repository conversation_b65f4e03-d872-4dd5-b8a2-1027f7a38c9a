2025-10-01 09:36:15,195 - INFO - ============================================================
2025-10-01 09:36:15,195 - INFO - 开始使用 pypandoc 进行 Markdown 到 Word 文档转换
2025-10-01 09:36:15,195 - INFO - ============================================================
2025-10-01 09:36:15,197 - INFO - 步骤 1/5: 验证输入文件
2025-10-01 09:36:15,197 - INFO - 输入文件验证成功: D:\Annet\医院对接\中山大学附属口腔医院\项目分析\云胶片链接生成\云胶片对接简版文档.md (大小: 4390 字节)
2025-10-01 09:36:15,197 - INFO - 步骤 2/5: 安装 pypandoc
2025-10-01 09:36:15,197 - INFO - 正在安装 pypandoc...
2025-10-01 09:36:15,214 - INFO - pypandoc 已安装
2025-10-01 09:36:15,221 - INFO - 步骤 3/5: 设置 pandoc 环境
2025-10-01 09:36:15,221 - INFO - 正在设置 pandoc 环境...
2025-10-01 09:36:15,237 - INFO - See http://johnmacfarlane.net/pandoc/installing.html
for installation options

2025-10-01 09:36:15,237 - INFO - ---------------------------------------------------------------


2025-10-01 09:36:15,237 - INFO - pandoc 未安装，开始下载...
2025-10-01 09:36:15,241 - INFO - 下载尝试 1/3
2025-10-01 09:36:15,241 - INFO - 尝试手动下载 pandoc...
2025-10-01 09:36:15,241 - INFO - 尝试从 https://github.com/jgm/pandoc/releases/download/3.1.8/pandoc-3.1.8-windows-x86_64.zip 下载...
2025-10-01 09:36:15,241 - INFO - Downloading pandoc from https://github.com/jgm/pandoc/releases/download/3.1.8/pandoc-3.1.8-windows-x86_64.zip ...
2025-10-01 09:36:30,149 - INFO - Unpacking pandoc-3.1.8-windows-x86_64.zip to tempfolder...
2025-10-01 09:36:41,149 - WARNING - 从 https://github.com/jgm/pandoc/releases/download/3.1.8/pandoc-3.1.8-windows-x86_64.zip 下载失败: Command '['msiexec', '/a', 'pandoc-3.1.8-windows-x86_64.zip', '/qb', 'TARGETDIR=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp15blays7']' returned non-zero exit status 1620.
2025-10-01 09:36:41,149 - INFO - 尝试从 https://github.com/jgm/pandoc/releases/download/2.19.2/pandoc-2.19.2-windows-x86_64.zip 下载...
2025-10-01 09:36:41,153 - INFO - Downloading pandoc from https://github.com/jgm/pandoc/releases/download/2.19.2/pandoc-2.19.2-windows-x86_64.zip ...
2025-10-01 09:37:17,803 - INFO - Unpacking pandoc-2.19.2-windows-x86_64.zip to tempfolder...
2025-10-01 09:37:29,177 - WARNING - 从 https://github.com/jgm/pandoc/releases/download/2.19.2/pandoc-2.19.2-windows-x86_64.zip 下载失败: Command '['msiexec', '/a', 'pandoc-2.19.2-windows-x86_64.zip', '/qb', 'TARGETDIR=C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpa4yri025']' returned non-zero exit status 1620.
2025-10-01 09:37:29,177 - INFO - 尝试默认下载方式...
2025-10-01 09:37:33,415 - INFO - Downloading pandoc from https://github.com/jgm/pandoc/releases/download/3.8.1/pandoc-3.8.1-windows-x86_64.msi ...
2025-10-01 09:37:50,328 - INFO - Unpacking pandoc-3.8.1-windows-x86_64.msi to tempfolder...
2025-10-01 09:37:54,315 - INFO - Copying pandoc.exe to C:\Users\<USER>\AppData\Local\Pandoc ...
2025-10-01 09:37:54,558 - INFO - Copying COPYRIGHT.txt to C:\Users\<USER>\AppData\Local\Pandoc ...
2025-10-01 09:37:54,608 - INFO - Done.
2025-10-01 09:37:54,612 - INFO - pandoc 默认下载成功
2025-10-01 09:37:56,835 - INFO - pandoc 下载并验证成功，版本: 3.8.1
2025-10-01 09:37:56,835 - INFO - 步骤 4/5: 执行文档转换
2025-10-01 09:37:56,836 - INFO - 开始转换: D:\Annet\医院对接\中山大学附属口腔医院\项目分析\云胶片链接生成\云胶片对接简版文档.md -> D:\Annet\医院对接\中山大学附属口腔医院\项目分析\云胶片链接生成\云胶片对接简版文档.docx
2025-10-01 09:37:57,075 - ERROR - pypandoc 转换失败: Pandoc died with exitcode "83" during conversion: [WARNING] Deprecated: --highlight-style. Use --syntax-highlighting instead.

Error running filter :

cannot open : Invalid argument


2025-10-01 09:37:57,075 - ERROR - 文档转换失败

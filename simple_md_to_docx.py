#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版 Markdown 到 Word 文档转换器
使用 python-docx 库直接解析 Markdown 并生成 Word 文档

作者: AI Assistant
日期: 2025-10-01
功能: 将云胶片对接简版文档.md 转换为 Word 格式
"""

import os
import sys
import re
import subprocess
import logging
from pathlib import Path
from typing import List, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('conversion.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class SimpleMarkdownToDocxConverter:
    """简化版 Markdown 到 Word 文档转换器"""
    
    def __init__(self):
        """初始化转换器"""
        self.input_file = "云胶片对接简版文档.md"
        self.output_file = "云胶片对接简版文档.docx"
        self.current_dir = Path.cwd()
        
    def install_dependencies(self) -> bool:
        """
        安装必要的依赖包
        
        Returns:
            bool: 安装是否成功
        """
        logger.info("正在检查并安装依赖...")
        
        packages = ['python-docx', 'markdown']
        
        for package in packages:
            try:
                __import__(package.replace('-', '_'))
                logger.info(f"{package} 已安装")
            except ImportError:
                logger.info(f"{package} 未安装，正在安装...")
                try:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                    logger.info(f"{package} 安装成功")
                except Exception as e:
                    logger.error(f"{package} 安装失败: {e}")
                    return False
        
        return True
    
    def parse_markdown_content(self, content: str) -> List[Tuple[str, str]]:
        """
        解析 Markdown 内容
        
        Args:
            content: Markdown 文本内容
            
        Returns:
            List[Tuple[str, str]]: 解析后的内容列表，每个元素为 (类型, 内容)
        """
        lines = content.split('\n')
        parsed_content = []
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # 空行
            if not line:
                parsed_content.append(('paragraph', ''))
                i += 1
                continue
            
            # 标题
            if line.startswith('#'):
                level = len(line) - len(line.lstrip('#'))
                title = line.lstrip('#').strip()
                parsed_content.append((f'heading{level}', title))
                i += 1
                continue
            
            # 代码块
            if line.startswith('```'):
                code_lines = []
                i += 1
                while i < len(lines) and not lines[i].strip().startswith('```'):
                    code_lines.append(lines[i])
                    i += 1
                parsed_content.append(('code', '\n'.join(code_lines)))
                i += 1
                continue
            
            # 表格
            if '|' in line:
                table_lines = []
                while i < len(lines) and '|' in lines[i]:
                    table_lines.append(lines[i].strip())
                    i += 1
                parsed_content.append(('table', table_lines))
                continue
            
            # 列表项
            if line.startswith(('- ', '* ', '+ ')) or re.match(r'^\d+\.', line):
                list_items = []
                while i < len(lines) and (lines[i].strip().startswith(('- ', '* ', '+ ')) or re.match(r'^\d+\.', lines[i].strip())):
                    item = lines[i].strip()
                    if item.startswith(('- ', '* ', '+ ')):
                        list_items.append(('bullet', item[2:].strip()))
                    else:
                        list_items.append(('number', re.sub(r'^\d+\.\s*', '', item)))
                    i += 1
                parsed_content.append(('list', list_items))
                continue
            
            # 普通段落
            parsed_content.append(('paragraph', line))
            i += 1
        
        return parsed_content
    
    def create_docx_document(self, parsed_content: List[Tuple[str, str]]) -> bool:
        """
        创建 Word 文档
        
        Args:
            parsed_content: 解析后的内容
            
        Returns:
            bool: 创建是否成功
        """
        try:
            from docx import Document
            from docx.shared import Inches, Pt
            from docx.enum.text import WD_ALIGN_PARAGRAPH
            from docx.oxml.shared import OxmlElement, qn
            
            # 创建文档
            doc = Document()
            
            # 设置文档样式
            style = doc.styles['Normal']
            font = style.font
            font.name = '微软雅黑'
            font.size = Pt(11)
            
            for content_type, content in parsed_content:
                if content_type == 'heading1':
                    heading = doc.add_heading(content, level=1)
                    heading.alignment = WD_ALIGN_PARAGRAPH.CENTER
                elif content_type == 'heading2':
                    doc.add_heading(content, level=2)
                elif content_type == 'heading3':
                    doc.add_heading(content, level=3)
                elif content_type == 'paragraph':
                    if content:
                        p = doc.add_paragraph(content)
                        # 处理粗体文本
                        if '**' in content:
                            p.clear()
                            parts = content.split('**')
                            for i, part in enumerate(parts):
                                run = p.add_run(part)
                                if i % 2 == 1:  # 奇数索引为粗体
                                    run.bold = True
                    else:
                        doc.add_paragraph()
                elif content_type == 'code':
                    p = doc.add_paragraph()
                    run = p.add_run(content)
                    run.font.name = 'Consolas'
                    run.font.size = Pt(9)
                    # 设置代码块背景色
                    shading_elm = OxmlElement('w:shd')
                    shading_elm.set(qn('w:fill'), 'F5F5F5')
                    run._element.get_or_add_rPr().append(shading_elm)
                elif content_type == 'table':
                    self._create_table(doc, content)
                elif content_type == 'list':
                    for item_type, item_content in content:
                        if item_type == 'bullet':
                            doc.add_paragraph(item_content, style='List Bullet')
                        else:
                            doc.add_paragraph(item_content, style='List Number')
            
            # 保存文档
            output_path = self.current_dir / self.output_file
            doc.save(str(output_path))
            logger.info(f"Word 文档已保存: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"创建 Word 文档时发生错误: {e}")
            return False
    
    def _create_table(self, doc, table_lines: List[str]):
        """
        创建表格
        
        Args:
            doc: Word 文档对象
            table_lines: 表格行数据
        """
        try:
            # 过滤掉分隔行
            data_lines = [line for line in table_lines if not re.match(r'^[\|\-\s]+$', line)]
            
            if len(data_lines) < 2:
                return
            
            # 解析表格数据
            rows = []
            for line in data_lines:
                cells = [cell.strip() for cell in line.split('|') if cell.strip()]
                if cells:
                    rows.append(cells)
            
            if not rows:
                return
            
            # 创建表格
            table = doc.add_table(rows=len(rows), cols=len(rows[0]))
            table.style = 'Table Grid'
            
            # 填充数据
            for i, row_data in enumerate(rows):
                for j, cell_data in enumerate(row_data):
                    if j < len(table.rows[i].cells):
                        cell = table.rows[i].cells[j]
                        cell.text = cell_data
                        # 表头加粗
                        if i == 0:
                            for paragraph in cell.paragraphs:
                                for run in paragraph.runs:
                                    run.bold = True
            
        except Exception as e:
            logger.error(f"创建表格时发生错误: {e}")
    
    def validate_files(self) -> bool:
        """
        验证输入和输出文件
        
        Returns:
            bool: 验证是否成功
        """
        input_path = self.current_dir / self.input_file
        
        # 检查输入文件
        if not input_path.exists():
            logger.error(f"输入文件不存在: {input_path}")
            return False
        
        # 检查输出文件
        output_path = self.current_dir / self.output_file
        if output_path.exists():
            file_size = output_path.stat().st_size
            if file_size > 0:
                logger.info(f"输出文件验证成功: {output_path} (大小: {file_size} 字节)")
                return True
        
        return False
    
    def run(self) -> bool:
        """
        执行完整的转换流程
        
        Returns:
            bool: 转换是否成功
        """
        logger.info("=" * 50)
        logger.info("开始简化版 Markdown 到 Word 文档转换")
        logger.info("=" * 50)
        
        # 步骤1: 安装依赖
        if not self.install_dependencies():
            logger.error("依赖安装失败")
            return False
        
        # 步骤2: 读取 Markdown 文件
        try:
            input_path = self.current_dir / self.input_file
            with open(input_path, 'r', encoding='utf-8') as f:
                content = f.read()
            logger.info(f"成功读取输入文件: {input_path}")
        except Exception as e:
            logger.error(f"读取输入文件失败: {e}")
            return False
        
        # 步骤3: 解析 Markdown 内容
        try:
            parsed_content = self.parse_markdown_content(content)
            logger.info(f"成功解析 Markdown 内容，共 {len(parsed_content)} 个元素")
        except Exception as e:
            logger.error(f"解析 Markdown 内容失败: {e}")
            return False
        
        # 步骤4: 创建 Word 文档
        if not self.create_docx_document(parsed_content):
            logger.error("创建 Word 文档失败")
            return False
        
        # 步骤5: 验证输出文件
        if not self.validate_files():
            logger.error("输出文件验证失败")
            return False
        
        logger.info("=" * 50)
        logger.info("转换完成！")
        logger.info(f"输出文件: {self.current_dir / self.output_file}")
        logger.info("=" * 50)
        
        return True


def main():
    """主函数"""
    try:
        converter = SimpleMarkdownToDocxConverter()
        success = converter.run()
        
        if success:
            print("\n✅ 转换成功完成！")
            print(f"📄 输出文件: {converter.output_file}")
            sys.exit(0)
        else:
            print("\n❌ 转换失败，请查看日志了解详情")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行过程中发生未预期的错误: {e}")
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Markdown 到 Word 文档转换器
使用 pandoc 将 Markdown 文件转换为 Microsoft Word 格式的 .docx 文档

作者: AI Assistant
日期: 2025-10-01
功能: 将云胶片对接简版文档.md 转换为 Word 格式
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
from typing import Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('conversion.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class MarkdownToDocxConverter:
    """Markdown 到 Word 文档转换器类"""
    
    def __init__(self):
        """初始化转换器"""
        self.input_file = "云胶片对接简版文档.md"
        self.output_file = "云胶片对接简版文档.docx"
        self.current_dir = Path.cwd()
        
    def check_dependencies(self) -> bool:
        """
        检查并安装必要的依赖
        
        Returns:
            bool: 依赖检查是否成功
        """
        logger.info("正在检查依赖...")
        
        # 检查 pypandoc
        try:
            import pypandoc
            logger.info("pypandoc 已安装")
        except ImportError:
            logger.info("pypandoc 未安装，正在安装...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pypandoc"])
                import pypandoc
                logger.info("pypandoc 安装成功")
            except Exception as e:
                logger.error(f"pypandoc 安装失败: {e}")
                return False
        
        # 检查 pandoc 引擎
        try:
            import pypandoc
            # 尝试获取 pandoc 版本，如果失败会自动下载
            try:
                version = pypandoc.get_pandoc_version()
                logger.info(f"pandoc 版本: {version}")
            except OSError:
                logger.info("pandoc 未安装，正在下载...")
                pypandoc.download_pandoc()
                version = pypandoc.get_pandoc_version()
                logger.info(f"pandoc 下载完成，版本: {version}")
        except Exception as e:
            logger.error(f"pandoc 设置失败: {e}")
            return False
            
        return True
    
    def validate_input_file(self) -> bool:
        """
        验证输入文件是否存在
        
        Returns:
            bool: 文件验证是否成功
        """
        input_path = self.current_dir / self.input_file
        
        if not input_path.exists():
            logger.error(f"输入文件不存在: {input_path}")
            return False
            
        if not input_path.is_file():
            logger.error(f"输入路径不是文件: {input_path}")
            return False
            
        # 检查文件大小
        file_size = input_path.stat().st_size
        if file_size == 0:
            logger.error("输入文件为空")
            return False
            
        logger.info(f"输入文件验证成功: {input_path} (大小: {file_size} 字节)")
        return True
    
    def convert_to_docx(self) -> bool:
        """
        执行 Markdown 到 Word 的转换
        
        Returns:
            bool: 转换是否成功
        """
        try:
            import pypandoc
            
            input_path = str(self.current_dir / self.input_file)
            output_path = str(self.current_dir / self.output_file)
            
            logger.info(f"开始转换: {input_path} -> {output_path}")
            
            # 设置 pandoc 转换参数
            extra_args = [
                '--from=markdown',
                '--to=docx',
                '--standalone',
                '--reference-doc=',  # 可以指定模板文档
                '--highlight-style=tango',  # 代码高亮样式
                '--table-of-contents',  # 生成目录
                '--toc-depth=3',  # 目录深度
            ]
            
            # 执行转换
            pypandoc.convert_file(
                input_path,
                'docx',
                outputfile=output_path,
                extra_args=extra_args,
                encoding='utf-8'
            )
            
            logger.info("转换完成")
            return True
            
        except Exception as e:
            logger.error(f"转换过程中发生错误: {e}")
            return False
    
    def validate_output_file(self) -> bool:
        """
        验证输出文件是否成功生成
        
        Returns:
            bool: 输出文件验证是否成功
        """
        output_path = self.current_dir / self.output_file
        
        if not output_path.exists():
            logger.error(f"输出文件未生成: {output_path}")
            return False
            
        if not output_path.is_file():
            logger.error(f"输出路径不是文件: {output_path}")
            return False
            
        # 检查文件大小
        file_size = output_path.stat().st_size
        if file_size == 0:
            logger.error("输出文件为空")
            return False
            
        # 检查文件扩展名
        if output_path.suffix.lower() != '.docx':
            logger.error(f"输出文件扩展名错误: {output_path.suffix}")
            return False
            
        logger.info(f"输出文件验证成功: {output_path} (大小: {file_size} 字节)")
        return True
    
    def run(self) -> bool:
        """
        执行完整的转换流程
        
        Returns:
            bool: 整个转换流程是否成功
        """
        logger.info("=" * 50)
        logger.info("开始 Markdown 到 Word 文档转换")
        logger.info("=" * 50)
        
        # 步骤1: 检查依赖
        if not self.check_dependencies():
            logger.error("依赖检查失败")
            return False
        
        # 步骤2: 验证输入文件
        if not self.validate_input_file():
            logger.error("输入文件验证失败")
            return False
        
        # 步骤3: 执行转换
        if not self.convert_to_docx():
            logger.error("文档转换失败")
            return False
        
        # 步骤4: 验证输出文件
        if not self.validate_output_file():
            logger.error("输出文件验证失败")
            return False
        
        logger.info("=" * 50)
        logger.info("转换完成！")
        logger.info(f"输出文件: {self.current_dir / self.output_file}")
        logger.info("=" * 50)
        
        return True


def main():
    """主函数"""
    try:
        converter = MarkdownToDocxConverter()
        success = converter.run()
        
        if success:
            print("\n✅ 转换成功完成！")
            print(f"📄 输出文件: {converter.output_file}")
            sys.exit(0)
        else:
            print("\n❌ 转换失败，请查看日志了解详情")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行过程中发生未预期的错误: {e}")
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
